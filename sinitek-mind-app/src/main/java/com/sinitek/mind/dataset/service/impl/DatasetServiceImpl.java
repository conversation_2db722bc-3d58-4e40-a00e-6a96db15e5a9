package com.sinitek.mind.dataset.service.impl;

import com.sinitek.mind.dataset.convert.DatasetConvert;
import com.sinitek.mind.dataset.dto.*;
import com.sinitek.mind.dataset.entity.Dataset;
import com.sinitek.mind.dataset.entity.DatasetCollection;
import com.sinitek.mind.dataset.enumerate.DatasetStatusEnum;
import com.sinitek.mind.dataset.repository.DatasetCollectionRepository;
import com.sinitek.mind.dataset.repository.DatasetDataRepository;
import com.sinitek.mind.dataset.repository.DatasetRepository;
import com.sinitek.mind.dataset.service.IDatasetService;
import com.sinitek.mind.dataset.service.IDatasetTrainingService;
import com.sinitek.mind.model.service.ISystemModelService;
import com.sinitek.mind.support.permission.dto.AuthDTO;
import com.sinitek.mind.support.permission.service.IAuthService;
import com.sinitek.sirm.framework.exception.BussinessException;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 知识库服务实现类
 *
 * <AUTHOR>
 * date 2025-07-16
 */
@Service
public class DatasetServiceImpl implements IDatasetService {

    @Autowired
    private DatasetRepository datasetRepository;
    
    @Autowired
    private MongoTemplate mongoTemplate;
    
    @Autowired
    private ISystemModelService systemModelService;

    @Autowired
    private IDatasetTrainingService datasetTrainingService;

    @Autowired
    private DatasetConvert datasetConvert;

    @Autowired
    private IAuthService authService;

    @Autowired
    private DatasetCollectionRepository datasetCollectionRepository;

    @Autowired
    private DatasetDataRepository datasetDataRepository;

    /**
     * 创建知识库
     */
    @Override
    @Transactional
    public String createDataset(DatasetCreateRequest request, String userId, String teamId, String tmbId) throws Exception {
        if (StringUtils.isBlank(request.getName())) {
            throw new IllegalArgumentException("知识库名称不能为空");
        }

        // 处理parentId: 如果parentId为空，使用datasetId

        // 创建知识库实体
        Dataset dataset = new Dataset();
        dataset.setName(request.getName());
        dataset.setType(request.getType());
        String parentId = request.getParentId();
        dataset.setParentId(StringUtils.isNotBlank(parentId) ? parentId : null);

        dataset.setTeamId(teamId);
        dataset.setTmbId(tmbId);
        dataset.setAvatar(request.getAvatar());
        dataset.setIntro(request.getIntro());
        dataset.setVectorModel(request.getVectorModel());
        dataset.setAgentModel(request.getAgentModel());
        dataset.setVlmModel(request.getVlmModel());
        dataset.setUpdateTime(new Date());
        dataset.setInheritPermission(true);

        // 如果是folder类型，不设置模型
        if (!"folder".equals(request.getType())) {
            // 可以添加默认模型设置，如果需要
        }

        // 保存知识库
        Dataset savedDataset = datasetRepository.save(dataset);

        return savedDataset.getId();
    }

    /**
     * 更新知识库
     *
     * @param request 更新请求
     * @param userId 用户ID
     * @param teamId 团队ID
     * @param tmbId 成员ID
     * @throws Exception 异常
     */
    @Override
    @Transactional
    public void updateDataset(DatasetUpdateRequest request, String userId, String teamId, String tmbId) throws Exception {
        if (StringUtils.isBlank(request.getId())) {
            throw new IllegalArgumentException("知识库ID不能为空");
        }

        // 查询知识库
        Optional<Dataset> optionalDataset = datasetRepository.findById(request.getId());
        if (optionalDataset.isEmpty()) {
            throw new IllegalArgumentException("知识库不存在");
        }

        Dataset dataset = optionalDataset.get();

        // 验证权限
        if (!dataset.getTeamId().equals(teamId)) {
            throw new IllegalArgumentException("无权操作该知识库");
        }

        // 只允许更新name、intro、avatar字段
        if (StringUtils.isNotBlank(request.getName())) {
            dataset.setName(request.getName());
        }

        if (request.getIntro() != null) {
            dataset.setIntro(request.getIntro());
        }

        if (StringUtils.isNotBlank(request.getAvatar())) {
            dataset.setAvatar(request.getAvatar());
        }

        dataset.setUpdateTime(new Date());

        // 保存更新
        datasetRepository.save(dataset);
    }

    /**
     * 删除知识库
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteDataset(String datasetId, String userId, String teamId, String tmbId) throws Exception {
        validateAndGetDatasetOrThrow(datasetId, teamId, "操作");

        // 1. 查询该知识库下所有集合
        List<DatasetCollection> collections = datasetCollectionRepository.findAllByDatasetId(datasetId);
        List<String> collectionIds = collections.stream().map(DatasetCollection::getId).collect(Collectors.toList());

        // 2. 删除所有集合下的数据
        if (!collectionIds.isEmpty()) {
            datasetDataRepository.deleteAllByCollectionIdIn(collectionIds);
        }

        // 3. 删除所有集合
        datasetCollectionRepository.deleteAllByDatasetId(datasetId);

        // 4. 删除与该知识库相关的所有训练数据
        datasetTrainingService.deleteByDatasetId(datasetId);

        // 5. 删除知识库本身
        datasetRepository.deleteById(datasetId);
    }

    /**
     * 获取知识库详情
     */
    @Override
    public DatasetDTO getDatasetDetail(String datasetId, String userId, String teamId, String tmbId) {
        Dataset dataset = validateAndGetDatasetOrThrow(datasetId, teamId, "访问");
        // 转换为DTO
        DatasetDTO datasetDTO = datasetConvert.convertToDTO(dataset);
        // 设置状态
        datasetDTO.setStatus(DatasetStatusEnum.ACTIVE);
        // 设置权限
        datasetDTO.setPermission(new DatasetPermissionDTO(15, tmbId.equals(dataset.getTmbId())));
        return datasetDTO;
    }

    /**
     * 获取知识库列表
     */
    @Override
    public List<Dataset> getDatasetList(DatasetListRequest request, String userId, String teamId, String tmbId) throws Exception {
        // 构建查询条件
        Query query = new Query();
        
        // 将字符串teamId转换为ObjectId
        query.addCriteria(Criteria.where("teamId").is(new ObjectId(teamId)));
        
        // 父级ID条件
        if (StringUtils.isNotBlank(request.getParentId())) {
            query.addCriteria(Criteria.where("parentId").is(new ObjectId(request.getParentId())));
        } else {
            query.addCriteria(Criteria.where("parentId").is(null));
        }
        
        // 类型条件
        if (StringUtils.isNotBlank(request.getType())) {
            query.addCriteria(Criteria.where("type").is(request.getType()));
        }
        
        // 搜索关键词
        if (StringUtils.isNotBlank(request.getSearchKey())) {
            Pattern pattern = Pattern.compile(request.getSearchKey(), Pattern.CASE_INSENSITIVE);
            query.addCriteria(new Criteria().orOperator(
                    Criteria.where("name").regex(pattern),
                    Criteria.where("intro").regex(pattern)
            ));
        }
        
        // 添加根据updateTime字段从新到旧排序
        query.with(Sort.by(Sort.Order.desc("updateTime")));
        
        // 查询数据
        return mongoTemplate.find(query, Dataset.class);
    }

    /**
     * 获取知识库路径
     */
    @Override
    public List<DatasetPathDTO> getDatasetPaths(String datasetId, String userId, String teamId, String tmbId) throws Exception {
        List<DatasetPathDTO> paths = new ArrayList<>();
        String currentId = datasetId;
        while (StringUtils.isNotBlank(currentId)) {
            Optional<Dataset> optionalDataset = datasetRepository.findById(currentId);
            if (optionalDataset.isEmpty()) {
                break;
            }
            Dataset dataset = optionalDataset.get();
            if (!dataset.getTeamId().equals(teamId)) {
                break;
            }
            DatasetPathDTO dto = new DatasetPathDTO();
            dto.setParentId(dataset.getId());
            dto.setParentName(dataset.getName());
            paths.add(0, dto);
            currentId = dataset.getParentId() != null ? dataset.getParentId() : null;
        }
        return paths;
    }

    /**
     * 通过知识库名称查询知识库ID
     *
     * @param name 知识库名称
     * @return 知识库ID
     * <AUTHOR>
     * date 2025-07-23
     */
    @Override
    public String getDatasetIdByName(String name) {
        if (StringUtils.isBlank(name)) {
            throw new IllegalArgumentException("知识库名称不能为空");
        }
        Query query = new Query();
        query.addCriteria(Criteria.where("name").is(name));
        Dataset dataset = mongoTemplate.findOne(query, Dataset.class);
        if (dataset == null) {
            return "";
        }
        return dataset.getId();
    }

    @Override
    public DatasetDTO checkDatasetAuthByCurrentUser(String datasetId) {
        AuthDTO auth = authService.authCert();
        DatasetDTO dataset = this.getDatasetDetail(datasetId, auth.getUserId(), auth.getTeamId(), auth.getTmbId());
        if (!dataset.getPermission().getHasWritePer()) {
            throw new BussinessException("无写入权限");
        }
        return dataset;
    }

    @Override
    public String getVectorModelByDatasetId(String datasetId) {
        if (StringUtils.isBlank(datasetId)) {
            throw new IllegalArgumentException("datasetId不能为空");
        }
        Optional<Dataset> optional = datasetRepository.findById(datasetId);
        if (!optional.isPresent()) {
            throw new BussinessException("未找到指定ID的知识库: " + datasetId);
        }
        return optional.get().getVectorModel();
    }

    /**
     * 校验知识库ID、存在性和团队权限，返回Dataset对象
     *
     * @param datasetId 知识库ID
     * @param teamId 团队ID
     * @param actionDesc 操作描述（用于异常信息）
     * @return Dataset
     */
    private Dataset validateAndGetDatasetOrThrow(String datasetId, String teamId, String actionDesc) {
        if (StringUtils.isBlank(datasetId)) {
            throw new IllegalArgumentException("知识库ID不能为空");
        }
        Optional<Dataset> optionalDataset = datasetRepository.findById(datasetId);
        if (optionalDataset.isEmpty()) {
            throw new IllegalArgumentException("知识库不存在");
        }
        Dataset dataset = optionalDataset.get();
        if (!dataset.getTeamId().equals(teamId)) {
            throw new IllegalArgumentException("无权" + actionDesc + "该知识库");
        }
        return dataset;
    }

    /**
     * 查找知识库及其所有子知识库
     *
     * @param teamId 团队ID
     * @param datasetId 知识库ID
     * @return 知识库列表
     */
    private List<Dataset> findDatasetAndAllChildren(String teamId, String datasetId) {
        List<Dataset> result = new ArrayList<>();
        
        // 获取当前知识库
        Optional<Dataset> current = datasetRepository.findById(datasetId);
        if (current.isPresent() && current.get().getTeamId().equals(teamId)) {
            result.add(current.get());
            
            // 递归获取所有子知识库
            findAllChildren(teamId, datasetId, result);
        }
        
        return result;
    }
    
    /**
     * 递归查找所有子知识库
     *
     * @param teamId 团队ID
     * @param parentId 父ID
     * @param result 结果集
     */
    private void findAllChildren(String teamId, String parentId, List<Dataset> result) {
        List<Dataset> children = datasetRepository.findAllByTeamIdAndParentId(new ObjectId(teamId), new ObjectId(parentId));
        if (children != null && !children.isEmpty()) {
            result.addAll(children);
            
            for (Dataset child : children) {
                findAllChildren(teamId, child.getId(), result);
            }
        }
    }
}