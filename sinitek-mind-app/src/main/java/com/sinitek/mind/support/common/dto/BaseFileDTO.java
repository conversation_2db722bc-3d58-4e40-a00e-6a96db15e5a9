package com.sinitek.mind.support.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 基础文件信息DTO
 *
 * <AUTHOR>
 * date 2025-08-01
 */
@Data
@Schema(description = "基础文件信息DTO")
public class BaseFileDTO {

    @Schema(description = "文件ID")
    private String _id;

    @Schema(description = "文件ID")
    private String fileId;

    @Schema(description = "文件名")
    private String filename;

    @Schema(description = "文件Content-Type类型")
    private String contentType;

    @Schema(description = "文件长度/字节数")
    private Long length;

    @Schema(description = "文件编码（如有）")
    private String encoding;

}
