package com.sinitek.mind.support.permission.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.sinitek.cloud.sirmapp.right.dto.RightAuthDTO;
import com.sinitek.mind.core.app.enumerate.AppTypeEnum;
import com.sinitek.mind.core.app.repository.AppRepository;
import com.sinitek.mind.support.permission.constant.PermissionConstant;
import com.sinitek.mind.support.permission.dto.Permission;
import com.sinitek.mind.support.permission.dto.PermissionDTO;
import com.sinitek.mind.support.permission.dto.TeamPermissionDTO;
import com.sinitek.mind.support.permission.enumerate.AuthTypeEnum;
import com.sinitek.mind.support.permission.enumerate.ResourceTypeEnum;
import com.sinitek.mind.support.permission.enumerate.TeamResourceEnum;
import com.sinitek.mind.support.permission.service.IPermissionService;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.enumerate.CommonBooleanEnum;
import com.sinitek.sirm.org.dto.OrgObjectDTO;
import com.sinitek.sirm.org.dto.OrgObjectInfoDTO;
import com.sinitek.sirm.org.service.IOrgService;
import com.sinitek.sirm.org.service.IRightService;
import com.sinitek.sirm.right.IRightExtService;
import com.sinitek.spirit.org.core.enumerate.UnitTypeEnum;
import com.sinitek.spirit.right.server.entity.RightAuth;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class PermissionServiceImpl implements IPermissionService {

    private static final List<String> APP_FOLDER_TYPE_LIST = List.of(AppTypeEnum.FOLDER.getValue(), AppTypeEnum.HTTP_PLUGIN.getValue());

    private static final String[] ALL_AUTH_TYPE = new String[]{
        AuthTypeEnum.WRITE.getName(),
        AuthTypeEnum.READ.getName(),
        AuthTypeEnum.MANAGE.getName()
    };

    @Autowired
    private IOrgService orgService;

    @Autowired
    private IRightExtService rightExtService;

    @Autowired
    private IRightService rightService;

    @Autowired
    private final AppRepository appRepository;

    @Override
    public PermissionDTO getPermissionResultDTO(String orgId, String resourceId,
                                                String resourceType) {
        if (StringUtils.isEmpty(orgId)) {
            log.warn("获取指定对象权限结果DTO,orgId不能为空");
            return new TeamPermissionDTO();
        }

        // type=3,表示查询上级节点和自身的权限
        List<RightAuth> authDTOList = rightService.findAllAuthList(orgId, resourceType,
            ALL_AUTH_TYPE, 3, false);

        if (CollUtil.isEmpty(authDTOList)) {
            return new PermissionDTO();
        }

        List<RightAuth> filterAuthList = authDTOList.stream()
            .filter(dto -> ObjectUtil.equals(resourceId, dto.getObjectKey()))
            .toList();

        if (CollUtil.isEmpty(filterAuthList)) {
            return new PermissionDTO();
        }

        boolean isManage = false;
        boolean isWrite = false;
        boolean isRead = false;

        // 只要有一个权限对象有权限，就说明有权限
        for (RightAuth authDTO : filterAuthList) {
            if (AuthTypeEnum.MANAGE.getName().equals(authDTO.getRightType())) {
                isManage = true;
            } else if (AuthTypeEnum.WRITE.getName().equals(authDTO.getRightType())) {
                isWrite = true;
            } else if (AuthTypeEnum.READ.getName().equals(authDTO.getRightType())){
                isRead = true;
            }
        }

        PermissionDTO resultDTO = new PermissionDTO();
        if (isManage) {
            resultDTO.setValue(PermissionConstant.MANAGER_PER);
        } else if (isWrite) {
            resultDTO.setValue(PermissionConstant.WRITE_PER);
        } else if (isRead) {
            resultDTO.setValue(PermissionConstant.READ_PER);
        } else {
            resultDTO.setValue(PermissionConstant.NULL_PERMISSION);
        }

        return resultDTO;
    }

    @Override
    public TeamPermissionDTO getTeamPermissionResultDTO(String orgId) {
        if (StringUtils.isEmpty(orgId)) {
            log.warn("获取指定对象权限结果DTO,orgId不能为空");
            return new TeamPermissionDTO();
        }

        // 团队权限中的权限类型是固定的
        // type=3,表示查询上级节点和自身的权限
        List<RightAuth> authDTOList = rightService.findAllAuthList(orgId,
            ResourceTypeEnum.TEAM.getValue(), new String[]{AuthTypeEnum.TEAM_AUTH_TYPE.getName()}, 3, false);

        // 键为resourceId，值为是否有权限
        Map<String, Boolean> authMap = authDTOList.stream()
            .map(entity -> {
                    Boolean rejectFlag = CommonBooleanEnum.getBoolValueByValue(entity.getRejectFlag());
                return Tuples.of(entity.getObjectKey(), rejectFlag);
            })
            .collect(Collectors.toMap(Tuple2::getT1, Tuple2::getT2));

        return generateTeamPermissionResult(authMap);
    }

    @Override
    public Map<String, TeamPermissionDTO> getTeamPermissionResultDTO(List<String> orgIds) {
        if (CollUtil.isEmpty(orgIds)) {
            log.warn("获取指定对象权限结果DTO,orgIds不能为空");
            return Map.of();
        }

        return orgIds.stream()
            .map(orgId -> Tuples.of(orgId, getTeamPermissionResultDTO(orgId)))
            .collect(Collectors.toMap(Tuple2::getT1, Tuple2::getT2));
    }

    @Override
    public List<Permission> findAllAuthedResource(String orgId,
                                                  List<ResourceTypeEnum> resourceTypeList, List<AuthTypeEnum> authTypeList) {

        if (StringUtils.isBlank(orgId) || CollUtil.isEmpty(resourceTypeList) || CollUtil.isEmpty(
            authTypeList)) {
            return List.of();
        }

        List<RightAuth> result = new LinkedList<>();
        for (ResourceTypeEnum resourceTypeEnum : resourceTypeList) {
            for (AuthTypeEnum authTypeEnum : authTypeList) {
                result.addAll(rightService.findEnableAuthedObjects(orgId,
                    resourceTypeEnum.getValue(), authTypeEnum.getName(), null));
            }
        }

        return result.stream()
            .map(this::convertRightAuth2PermissionDTO)
            .toList();
    }

    /**
     * 获取资源授权给的对象 支持app和dataset使用，团队较为特殊
     * 当资源类型为Team时，则资源id为团队id
     * @param resourceId 资源id
     * @param resourceTypeEnum 资源类型
     * @return 已授权的对象-权限结果
     */
    @Override
    public Map<String, ? extends PermissionDTO> findAllAuthedOrg(String resourceId,
                                                                 ResourceTypeEnum resourceTypeEnum) {
        // 优先判断是不是团队
        if (ObjectUtil.equals(resourceTypeEnum, ResourceTypeEnum.TEAM )) {
            // 每一个授权的组织，在数据库中都会存储四种团队资源的记录，所有这里的团队资源选择任意一个都可以
            List<String> authOrgIdList = rightService.findAuthOrgIdList(
                TeamResourceEnum.MANAGER.getId(), TeamResourceEnum.MANAGER.getType(),
                AuthTypeEnum.TEAM_AUTH_TYPE.getName());

            return getTeamPermissionResultDTO(authOrgIdList);
        }

        if (StringUtils.isBlank(resourceId) || resourceTypeEnum == null) {
            return Map.of();
        }

        Map<String, PermissionDTO> allResult = new HashMap<>();

        // app和Dataset
        for (String authType : ALL_AUTH_TYPE) {
            List<OrgObjectDTO> orgDTOList = rightService.findEnableAuthOrgObjects(
                resourceId, resourceTypeEnum.getValue(), authType);

            if (CollUtil.isEmpty(orgDTOList)) {
                continue;
            }

            // 转换
            allResult.putAll(orgDTOList.stream()
                .map(dto -> {
                    PermissionDTO resultDTO = getPermissionResultDTO(
                        dto.getOrgId(), resourceId, resourceTypeEnum.getValue());
                    return Tuples.of(dto.getOrgId(), resultDTO);
                })
                .collect(Collectors.toMap(Tuple2::getT1, Tuple2::getT2)));

        }

        return allResult;
    }


    /**
     * 保存权限 当资源类型为team时，资源id则为teamId
     *
     * @param resourceId 资源id
     * @param resourceTypeEnum 资源类型
     * @param orgIdList 需要保存权限的对象，成员id、部门id、群组id
     * @param per 权限值
     */
    @Override
    public void savePermission(String resourceId, ResourceTypeEnum resourceTypeEnum,
        List<String> orgIdList, long per) {

        if (CollUtil.isEmpty(orgIdList)) {
            // 不需要在添加权限了
            log.warn("orgIdList为空，无法保存权限");
            return;
        }

        // 先删除对应资源的全部权限
        List<String> needDeleteOrgId = new LinkedList<>();


        if (ObjectUtil.equals(ResourceTypeEnum.TEAM, resourceTypeEnum)) {
            saveTeamPermission(resourceId, orgIdList, per);
            return;
        }
        // app和dataset使用
        for (String authType : ALL_AUTH_TYPE) {
            List<OrgObjectInfoDTO> allAuthOrgObjects = rightService.findAllAuthOrgObjects(
                resourceId, resourceTypeEnum.getValue(), authType);

            needDeleteOrgId.addAll(allAuthOrgObjects.stream()
                .map(OrgObjectInfoDTO::getOrgid)
                .toList());
        }

        needDeleteOrgId = needDeleteOrgId.stream()
            .filter(orgIdList::contains)
            .toList();

        // 删除指定资源的权限
        deletePermission(resourceId, resourceTypeEnum, needDeleteOrgId);

        // 从权限值中计算出，要保存的authType
        AuthTypeEnum authTypeEnum = calcAuthType(per);

        List<RightAuthDTO> needSaveList = orgIdList.stream()
            .map(orgId -> {
                RightAuthDTO rightAuthDTO = new RightAuthDTO();
                rightAuthDTO.setAuthOrgId(orgId);
                rightAuthDTO.setObjectKey(resourceId);
                rightAuthDTO.setRightDefineKey(resourceTypeEnum.getValue());
                rightAuthDTO.setRejectFlag(false);
                rightAuthDTO.setRightType(authTypeEnum.getName());

                return rightAuthDTO;
            })
            .toList();
        rightExtService.saveRightAuthList(needSaveList);
    }

    /**
     * 保存团队的权限
     * @param teamId
     * @param orgIdList
     * @param per
     */
    private void saveTeamPermission(String teamId, List<String> orgIdList, long per) {
        // teamId就是多租户id，不需要手动处理
        // 团队这边的权限，需要修改rejectFlag，来表示是否含有权限
        if (CollUtil.isEmpty(orgIdList)) {
            log.info("保存团队权限时，组织id列表为空，不进行修改");
            return;
        }
        orgIdList.forEach(orgId -> {
            List<RightAuthDTO> needDeleteList = rightExtService.findAuthedObjects(orgId
                , ResourceTypeEnum.TEAM.getValue(), AuthTypeEnum.TEAM_AUTH_TYPE.getName(), null, false);

            // 先删除，在新建
            rightExtService.deleteRightAuthList(needDeleteList);

            // 新建并保存
            List<RightAuthDTO> needSaveList = generateTeamRightAuthDTOList(orgId, per);
            rightExtService.saveRightAuthList(needSaveList);
        });

    }

    /**
     * 根据权限值，生成RightAuthDTO列表进行保存
     * @param orgId 组织id
     * @param per 权限值
     * @return
     */
    private List<RightAuthDTO> generateTeamRightAuthDTOList(String orgId, long per) {
        List<RightAuthDTO> result = new LinkedList<>();
        RightAuthDTO appCreate = generateTeamRightAuthDTO(orgId, TeamResourceEnum.APP_CREATE);
        RightAuthDTO apikeyCreate = generateTeamRightAuthDTO(orgId, TeamResourceEnum.APIKEY_CREATE);
        RightAuthDTO datasetCreate = generateTeamRightAuthDTO(orgId, TeamResourceEnum.DATASET_CREATE);
        RightAuthDTO manager = generateTeamRightAuthDTO(orgId, TeamResourceEnum.MANAGER);

        // 因为表示的是否被拒绝，所有应该为不等于号。没有权限时，结果为true，有权限时，结果为false
        appCreate.setRejectFlag((PermissionConstant.APP_CREATE_PER & per) != PermissionConstant.APP_CREATE_PER);
        apikeyCreate.setRejectFlag((
            PermissionConstant.APIKEY_CREATE_PER & per) != PermissionConstant.APIKEY_CREATE_PER);
        datasetCreate.setRejectFlag((
            PermissionConstant.DATASET_CREATE_PER & per) != PermissionConstant.DATASET_CREATE_PER);
        manager.setRejectFlag((PermissionConstant.TEAM_MANAGE_PER & per) != PermissionConstant.TEAM_MANAGE_PER);

        result.add(appCreate);
        result.add(apikeyCreate);
        result.add(datasetCreate);
        result.add(manager);
        return result;
    }

    /**
     * 生成团队权限DTO，默认无权限
     * @param orgId 组织id
     * @return 默认团队权限DTO
     */
    private RightAuthDTO generateTeamRightAuthDTO(String orgId, TeamResourceEnum resourceEnum) {
        RightAuthDTO dto = new RightAuthDTO();
        dto.setAuthOrgId(orgId);
        dto.setObjectKey(resourceEnum.getId());
        dto.setRightDefineKey(resourceEnum.getType());
        dto.setRejectFlag(true);
        dto.setRightType(AuthTypeEnum.TEAM_AUTH_TYPE.getName());
        return dto;
    }


    /**
     * 从权限值中计算出authType
     * 只有资源类型为app和dataset使用，团队的为固定值AuthTypeEnum.TEAM_AUTH_TYPE
     * @param per 权限值
     * @return authTypeEnum
     */
    private AuthTypeEnum calcAuthType(long per) {
        // app和dataset
        for (AuthTypeEnum authTypeEnum : AuthTypeEnum.values()) {
            if (ObjectUtil.equals(per, authTypeEnum.getValue())) {
                return authTypeEnum;
            }
        }

        if (per == PermissionConstant.OWNER_PERMISSION_VAL) {
            // 拥有者是在具体场景下判断设置的，数据库中只能存储一个管理权限
            return AuthTypeEnum.MANAGE;
        }

        // 默认权限
        return AuthTypeEnum.READ;
    }

    /**
     * 删除权限 当资源类型为team时，资源id则为teamId
     *
     * @param resourceId 资源id
     * @param resourceTypeEnum 资源类型
     * @param orgIdList 需要删除的对象，成员id、部门id、群组id
     */
    @Override
    public void deletePermission(String resourceId, ResourceTypeEnum resourceTypeEnum,
        List<String> orgIdList) {
        if (CollUtil.isEmpty(orgIdList)) {
            log.info("需要删除授权的对象为空，不进行删除授权");
            return;
        }
        if (ObjectUtil.equals(ResourceTypeEnum.TEAM, resourceTypeEnum)) {
            // 团队的删除逻辑
            for (String orgId : orgIdList) {
                for (TeamResourceEnum teamResourceEnum : TeamResourceEnum.values()) {
                    rightService.deleteRightAuthList(orgId, teamResourceEnum.getId(), teamResourceEnum.getType(), List.of(AuthTypeEnum.TEAM_AUTH_TYPE.getName()));
                }
            }
        }
        // app和dataset的删除逻辑
        orgIdList.forEach(orgId -> rightService.deleteRightAuthList(orgId, resourceId, resourceTypeEnum.getValue(), List.of(ALL_AUTH_TYPE)));
    }

    @Override
    public void deletePermission(String resourceId, ResourceTypeEnum resourceTypeEnum) {
        if (ObjectUtil.equals(ResourceTypeEnum.TEAM, resourceTypeEnum)) {
            // 团队的删除逻辑
            for (TeamResourceEnum teamResourceEnum : TeamResourceEnum.values()) {
                rightService.deleteRightAuth(teamResourceEnum.getId(), teamResourceEnum.getType(), new String[]{AuthTypeEnum.TEAM_AUTH_TYPE.getName()});
            }
        } else {
            // app和dataset的删除逻辑
            rightService.deleteRightAuth(resourceId, resourceTypeEnum.getValue(), ALL_AUTH_TYPE);
        }
    }

    private Permission convertRightAuth2PermissionDTO(
        RightAuth rightAuth) {
        String objectKey = rightAuth.getObjectKey();
        String rightDefineKey = rightAuth.getRightDefineKey();
        String rightType = rightAuth.getRightType();
        Long objId = rightAuth.getObjId();
        String orgId = rightAuth.getAuthOrgId();

        Permission permission = new Permission();
        permission.setId(String.valueOf(objId));
        permission.setTeamId(CurrentUserFactory.getTenantId());
        String unitType = orgService.getUnitTypeStrByOrgId(orgId);
        if (UnitTypeEnum.UNIT.toString().equals(unitType)) {
            permission.setOrgId(orgId);
        } else if (UnitTypeEnum.ROLE.toString().equals(unitType)) {
            permission.setGroupId(orgId);
        } else {
            permission.setTmbId(orgId);
        }
        permission.setResourceType(rightDefineKey);
        permission.setAuthType(rightType);
        permission.setResourceId(objectKey);

        long per = calcPerValueByAuthType(orgId, rightType);
        permission.setPermission(per);

        return permission;
    }

    /**
     * 计算权限值
     *
     * @param orgId 组织id，可以为-tmbId、deptId、groupId
     * @param authType 权限类型
     * @return 计算出的权限值
     */
    private long calcPerValueByAuthType(String orgId, String authType) {
        // 默认无权限
        long per = PermissionConstant.NULL_PERMISSION;
        if (AuthTypeEnum.READ.getName().equals(authType)) {
            // 只读
            per = PermissionConstant.READ_PER;
        } else if (AuthTypeEnum.WRITE.getName().equals(authType)) {
            // 写
            per = PermissionConstant.WRITE_PER;
        } else if (AuthTypeEnum.MANAGE.getName().equals(authType)) {
            // 管理
            per = PermissionConstant.MANAGER_PER;
        } else if (AuthTypeEnum.TEAM_AUTH_TYPE.getName().equals(authType)) {
            // 团队
            per = getTeamPermissionResultDTO(orgId).getValue();
        }
        return per;
    }

    /**
     * 从权限map，构建权限结果DTO
     * @param authMap 权限map
     */
    private TeamPermissionDTO generateTeamPermissionResult(Map<String, Boolean> authMap) {
        TeamPermissionDTO resultDTO = new TeamPermissionDTO();
        boolean appCreate = MapUtil.getBool(authMap, TeamResourceEnum.APP_CREATE.getId(), true);
        boolean datasetCreate = MapUtil.getBool(authMap, TeamResourceEnum.DATASET_CREATE.getId(), true);
        boolean apikeyCreate = MapUtil.getBool(authMap, TeamResourceEnum.APIKEY_CREATE.getId(), true);
        boolean manager = MapUtil.getBool(authMap, TeamResourceEnum.MANAGER.getId(), true);

//        resultDTO.setHasApikeyCreatePer(!apikeyCreate);
//        resultDTO.setHasAppCreatePer(!appCreate);
//        resultDTO.setHasDatasetCreatePer(!datasetCreate);
//        if (manager) {
//            // 被拒绝
//            resultDTO.setHasManagePer(false);
//            resultDTO.setHasReadPer(false);
//        } else {
//            // 未被拒绝
//            resultDTO.setHasManagePer(true);
//            resultDTO.setHasReadPer(true);
//        }
//        // 写权限一直是没有的
//        resultDTO.setHasWritePer(false);

        long per = PermissionConstant.NULL_PERMISSION;
        per += !manager ? PermissionConstant.TEAM_MANAGE_PER + PermissionConstant.TEAM_READ_PER : 0;
        per += !appCreate ? PermissionConstant.APP_CREATE_PER : 0;
        per += !datasetCreate ? PermissionConstant.DATASET_CREATE_PER : 0;
        per += !apikeyCreate ? PermissionConstant.APIKEY_CREATE_PER : 0;

        resultDTO.setValue(per);
//        resultDTO.set_permissionList(PermissionConstant.TEAM_PERMISSION_LIST);

        return resultDTO;
    }

}
