package com.sinitek.mind.core.app.repository;

import com.sinitek.mind.core.app.entity.AppVersion;
import jakarta.validation.constraints.NotBlank;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface AppVersionRepository extends MongoRepository<AppVersion, String> {
    AppVersion findFirstByAppIdAndIsPublishOrderByCreateTimeDesc(String appId, boolean isPublish);

    AppVersion findByIdAndAppId(String id, String appId);

    AppVersion findFirstByAppIdAndIsPublishAndIdGreaterThan(String appId, boolean b, ObjectId objectId);

    void deleteByAppId(String id);


    /**
     * 根据应用ID和版本ID查找版本
     * 用于插件预览节点功能
     */
    Optional<AppVersion> findByAppIdAndId(ObjectId appId, ObjectId id);

    /**
     * 根据应用ID查找最新版本
     * 用于插件预览节点功能
     */
     @Query("{'appId': ?0, 'isPublish': true}")
    Optional<AppVersion> findLatestByAppId(ObjectId appId);

    Page<AppVersion> findByAppIdAndIsPublishTrue(ObjectId appId, Pageable pageable);

    Page<AppVersion> findByAppIdAndIsPublish(@NotBlank(message = "应用ID不能为空") ObjectId appId, Boolean isPublish, Pageable pageable);

    long countByAppIdAndIsPublish(@NotBlank(message = "应用ID不能为空") ObjectId appId, Boolean isPublish);

    Page<AppVersion> findByAppId(@NotBlank(message = "应用ID不能为空") ObjectId appId, Pageable pageable);

    long countByAppId(@NotBlank(message = "应用ID不能为空") ObjectId appId);
    /**
     * 根据应用ID查找最新的已发布版本
     * 用于获取最新版本接口
     */
    @Query("{'appId': ?0, 'isPublish': true}")
    List<AppVersion> findLatestPublishByAppId(ObjectId appId, Sort sort);
}
